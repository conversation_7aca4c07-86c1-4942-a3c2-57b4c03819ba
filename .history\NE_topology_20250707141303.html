<!DOCTYPE html>
<html lang="zh">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>KUNPENG NE Topology</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
      background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
      color: #ffffff;
      min-height: 100vh;
      overflow-x: hidden;
    }

    /* 科技感背景动画 */
    .tech-bg {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: -1;
      opacity: 0.1;
    }

    .tech-grid {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-image: 
        linear-gradient(rgba(0, 255, 255, 0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(0, 255, 255, 0.1) 1px, transparent 1px);
      background-size: 50px 50px;
      animation: gridMove 20s linear infinite;
    }

    @keyframes gridMove {
      0% { transform: translate(0, 0); }
      100% { transform: translate(50px, 50px); }
    }

    /* 头部标题 */
    .header {
      text-align: center;
      padding: 30px 20px;
      background: linear-gradient(90deg, rgba(0, 255, 255, 0.1), rgba(0, 122, 204, 0.1));
      border-bottom: 2px solid rgba(0, 255, 255, 0.3);
      position: relative;
    }

    .header::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 2px;
      background: linear-gradient(90deg, transparent, #00ffff, transparent);
      animation: scanLine 3s ease-in-out infinite;
    }

    @keyframes scanLine {
      0%, 100% { opacity: 0; }
      50% { opacity: 1; }
    }

    .header h1 {
      font-size: 3.5rem;
      font-weight: 700;
      letter-spacing: 4px;
      background: linear-gradient(45deg, #00ffff, #0099ff, #00ffff, #66ccff);
      background-size: 400% 400%;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      animation: gradientShift 3s ease-in-out infinite, textGlow 2s ease-in-out infinite alternate;
      margin-bottom: 10px;
    }

    @keyframes gradientShift {
      0%, 100% { background-position: 0% 50%; }
      50% { background-position: 100% 50%; }
    }

    @keyframes textGlow {
      0% { filter: drop-shadow(0 0 10px rgba(0, 255, 255, 0.5)); }
      100% { filter: drop-shadow(0 0 30px rgba(0, 255, 255, 0.8)) drop-shadow(0 0 40px rgba(0, 153, 255, 0.6)); }
    }

    .header .subtitle {
      font-size: 1.5rem;
      font-weight: 500;
      background: linear-gradient(90deg, #ffffff, #00ffff, #ffffff);
      background-size: 200% 200%;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      animation: subtitleGlow 4s ease-in-out infinite;
      letter-spacing: 2px;
    }

    @keyframes subtitleGlow {
      0%, 100% { background-position: 0% 50%; }
      50% { background-position: 100% 50%; }
    }

    /* Tab导航 */
    .tabs-container {
      background: linear-gradient(90deg, rgba(0, 255, 255, 0.1), rgba(0, 122, 204, 0.2));
      padding: 0 20px;
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
      border-bottom: 2px solid rgba(0, 255, 255, 0.3);
    }

    .tabs {
      display: flex;
      max-width: 1800px;
      margin: 0 auto;
      overflow-x: auto;
    }

    .tab {
      padding: 15px 30px;
      margin-right: 5px;
      background: linear-gradient(90deg, rgba(0, 122, 204, 0.3), rgba(0, 255, 255, 0.2));
      border: none;
      border-radius: 12px 12px 0 0;
      cursor: pointer;
      font-weight: 600;
      color: #ffffff;
      font-size: 1rem;
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
      white-space: nowrap;
    }

    .tab::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      transition: left 0.3s ease;
    }

    .tab:hover::before {
      left: 100%;
    }

    .tab.active {
      background: linear-gradient(90deg, #007acc, #3399ff);
      box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
      transform: translateY(-2px);
    }

    .tab:hover {
      background: linear-gradient(90deg, rgba(0, 122, 204, 0.5), rgba(0, 255, 255, 0.3));
    }

    /* 内容区域 */
    .content {
      max-width: 1800px;
      margin: 0 auto;
      padding: 30px 20px;
    }

    .tab-content {
      display: none;
    }

    .tab-content.active {
      display: block;
      animation: fadeIn 0.5s ease-in-out;
    }

    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(20px); }
      to { opacity: 1; transform: translateY(0); }
    }

    .topology-title {
      font-size: 2.5rem;
      font-weight: 600;
      color: #00ffff;
      text-align: center;
      margin-bottom: 30px;
      text-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
    }

    /* 三个区域布局 */
    .topology-sections {
      display: grid;
      grid-template-columns: 1fr 1fr;
      grid-template-rows: auto auto;
      gap: 30px;
      margin-bottom: 30px;
    }

    .section {
      background: linear-gradient(145deg, rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.02));
      border: 1px solid rgba(0, 255, 255, 0.2);
      border-radius: 15px;
      padding: 25px;
      backdrop-filter: blur(10px);
      position: relative;
      transition: all 0.3s ease;
      overflow: hidden;
    }

    .section::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(0, 255, 255, 0.1), transparent);
      transition: left 0.5s ease;
    }

    .section:hover::before {
      left: 100%;
    }

    .section:hover {
      transform: translateY(-5px);
      border-color: rgba(0, 255, 255, 0.5);
      box-shadow: 0 10px 30px rgba(0, 255, 255, 0.2);
    }

    .section-title {
      font-size: 1.5rem;
      font-weight: 600;
      margin-bottom: 20px;
      color: #00ffff;
      text-align: center;
      position: relative;
    }

    .section-title::after {
      content: '';
      position: absolute;
      bottom: -5px;
      left: 50%;
      transform: translateX(-50%);
      width: 50px;
      height: 2px;
      background: linear-gradient(90deg, transparent, #00ffff, transparent);
    }

    /* 网元信息区域 */
    .ne-info {
      grid-column: 1;
      grid-row: 1;
    }

    .ne-list {
      list-style: none;
    }

    .ne-item {
      background: rgba(0, 122, 204, 0.1);
      border: 1px solid rgba(0, 255, 255, 0.2);
      border-radius: 8px;
      padding: 15px;
      margin-bottom: 15px;
      transition: all 0.3s ease;
    }

    .ne-item:hover {
      background: rgba(0, 122, 204, 0.2);
      border-color: rgba(0, 255, 255, 0.4);
      transform: translateX(5px);
    }

    .ne-name {
      font-size: 1.2rem;
      font-weight: 600;
      color: #00ffff;
      margin-bottom: 8px;
    }

    .ne-details {
      display: flex;
      justify-content: space-between;
      font-size: 0.9rem;
      color: rgba(255, 255, 255, 0.8);
    }

    /* 拓扑图区域 */
    .topo-image {
      grid-column: 2;
      grid-row: 1;
    }

    .topo-img {
      width: 100%;
      max-height: 400px;
      object-fit: contain;
      border-radius: 10px;
      border: 2px solid rgba(0, 255, 255, 0.3);
      background: rgba(0, 0, 0, 0.3);
    }

    .no-image {
      width: 100%;
      height: 300px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: rgba(0, 0, 0, 0.3);
      border: 2px dashed rgba(0, 255, 255, 0.3);
      border-radius: 10px;
      color: rgba(255, 255, 255, 0.5);
      font-size: 1.1rem;
    }

    /* 串口信息区域 */
    .console-info {
      grid-column: 1 / -1;
      grid-row: 2;
    }

    .console-sections {
      display: flex;
      flex-direction: column;
      gap: 30px;
    }

    /* 网元分组样式 */
    .network-console-group {
      background: rgba(0, 122, 204, 0.05);
      border: 2px solid rgba(0, 255, 255, 0.2);
      border-radius: 15px;
      padding: 25px;
      transition: all 0.3s ease;
    }

    .network-console-group:hover {
      background: rgba(0, 122, 204, 0.1);
      border-color: rgba(0, 255, 255, 0.4);
      transform: translateY(-3px);
      box-shadow: 0 8px 25px rgba(0, 255, 255, 0.15);
    }

    .network-title {
      font-size: 1.8rem;
      font-weight: 700;
      color: #00ffff;
      text-align: center;
      margin-bottom: 20px;
      padding-bottom: 10px;
      border-bottom: 2px solid rgba(0, 255, 255, 0.3);
      text-shadow: 0 0 15px rgba(0, 255, 255, 0.5);
    }

    .network-info {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 15px;
      margin-bottom: 25px;
      padding: 15px;
      background: rgba(0, 0, 0, 0.2);
      border-radius: 10px;
      border: 1px solid rgba(0, 255, 255, 0.1);
    }

    .network-info-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 12px;
      background: rgba(0, 122, 204, 0.1);
      border-radius: 5px;
      border: 1px solid rgba(0, 255, 255, 0.2);
    }

    .network-info-label {
      color: rgba(255, 255, 255, 0.7);
      font-weight: 500;
    }

    .network-info-value {
      color: #ffffff;
      font-weight: 600;
    }

    .console-slots {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 20px;
    }

    .console-slot {
      background: rgba(0, 122, 204, 0.1);
      border: 1px solid rgba(0, 255, 255, 0.2);
      border-radius: 10px;
      padding: 20px;
      transition: all 0.3s ease;
    }

    .console-slot:hover {
      background: rgba(0, 122, 204, 0.2);
      border-color: rgba(0, 255, 255, 0.4);
    }

    .slot-title {
      font-size: 1.2rem;
      font-weight: 600;
      color: #00ffff;
      margin-bottom: 15px;
      text-align: center;
    }

    .slot-info {
      margin-bottom: 15px;
      padding: 10px;
      background: rgba(0, 0, 0, 0.2);
      border-radius: 5px;
    }

    .slot-info-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 5px;
      font-size: 0.9rem;
    }

    .slot-info-label {
      color: rgba(255, 255, 255, 0.7);
    }

    .slot-info-value {
      color: #ffffff;
      font-weight: 500;
    }

    .log-links {
      list-style: none;
    }

    .log-link {
      display: block;
      padding: 8px 12px;
      margin-bottom: 5px;
      background: linear-gradient(45deg, rgba(0, 122, 204, 0.2), rgba(0, 255, 255, 0.1));
      border: 1px solid rgba(0, 255, 255, 0.3);
      border-radius: 5px;
      color: #ffffff;
      text-decoration: none;
      font-size: 0.85rem;
      transition: all 0.3s ease;
    }

    .log-link:hover {
      background: linear-gradient(45deg, rgba(0, 122, 204, 0.4), rgba(0, 255, 255, 0.2));
      border-color: rgba(0, 255, 255, 0.6);
      transform: translateX(5px);
    }

    /* 加载动画 */
    .loading {
      text-align: center;
      padding: 50px;
      color: #00ffff;
      font-size: 1.2rem;
    }

    .loading::after {
      content: '';
      display: inline-block;
      width: 20px;
      height: 20px;
      border: 2px solid rgba(0, 255, 255, 0.3);
      border-top: 2px solid #00ffff;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-left: 10px;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    /* 响应式设计 */
    @media (max-width: 1200px) {
      .topology-sections {
        grid-template-columns: 1fr;
        grid-template-rows: auto auto auto;
      }
      
      .ne-info {
        grid-column: 1;
        grid-row: 1;
      }
      
      .topo-image {
        grid-column: 1;
        grid-row: 2;
      }
      
      .console-info {
        grid-column: 1;
        grid-row: 3;
      }
    }

    @media (max-width: 768px) {
      .header h1 {
        font-size: 2.5rem;
      }
      
      .tabs {
        flex-wrap: wrap;
      }
      
      .tab {
        margin-bottom: 5px;
      }
      
      .console-sections {
        grid-template-columns: 1fr;
      }
    }
  </style>
</head>
<body>
  <div class="tech-bg">
    <div class="tech-grid"></div>
  </div>

  <div class="header">
    <h1>KUNPENG NE Topology</h1>
    <div class="subtitle">Network Element Topology Management</div>
  </div>

  <div class="tabs-container">
    <div class="tabs" id="topologyTabs">
      <div class="loading">加载拓扑配置中...</div>
    </div>
  </div>

  <div class="content">
    <div id="tabContents">
      <div class="loading">加载拓扑数据中...</div>
    </div>
  </div>

  <script>
    let topoConfig = {};
    let currentActiveTab = '';

    // 加载配置文件
    async function loadTopoConfig() {
      try {
        console.log('开始加载拓扑配置文件...');
        const response = await fetch('test_topo.json');

        if (!response.ok) {
          throw new Error('HTTP ' + response.status + ': ' + response.statusText);
        }

        topoConfig = await response.json();
        console.log('拓扑配置加载成功:', topoConfig);

        generateTabs();
        generateTabContents();
      } catch (error) {
        console.error('配置加载失败:', error);
        document.getElementById('topologyTabs').innerHTML = '<div class="loading" style="color: #ff6b6b;">配置加载失败: ' + error.message + '</div>';
        document.getElementById('tabContents').innerHTML = '<div class="loading" style="color: #ff6b6b;">请检查test_topo.json文件</div>';
      }
    }

    // 生成Tab导航
    function generateTabs() {
      const tabsContainer = document.getElementById('topologyTabs');
      tabsContainer.innerHTML = '';

      if (!topoConfig.Topology_Group || !Array.isArray(topoConfig.Topology_Group)) {
        tabsContainer.innerHTML = '<div class="loading" style="color: #ff6b6b;">配置格式错误</div>';
        return;
      }

      topoConfig.Topology_Group.forEach((group, index) => {
        const tab = document.createElement('button');
        tab.className = 'tab';
        tab.textContent = group.topology.toUpperCase();
        tab.dataset.topology = group.topology;

        if (index === 0) {
          tab.classList.add('active');
          currentActiveTab = group.topology;
        }

        tab.addEventListener('click', () => switchTab(group.topology));
        tabsContainer.appendChild(tab);
      });
    }

    // 生成Tab内容
    function generateTabContents() {
      const contentsContainer = document.getElementById('tabContents');
      contentsContainer.innerHTML = '';

      topoConfig.Topology_Group.forEach((group, index) => {
        const tabContent = document.createElement('div');
        tabContent.className = 'tab-content';
        tabContent.id = 'tab-' + group.topology;

        if (index === 0) {
          tabContent.classList.add('active');
        }

        tabContent.innerHTML = generateTopologyContent(group);
        contentsContainer.appendChild(tabContent);
      });
    }

    // 生成单个拓扑内容
    function generateTopologyContent(group) {
      const topology = group.topology;
      const networks = group.networks || [];

      let html = '<div class="topology-title">' + topology.toUpperCase() + ' Topology</div>';
      html += '<div class="topology-sections">';

      // 第一区域：网元信息
      html += '<div class="section ne-info">';
      html += '<h3 class="section-title">网元信息</h3>';
      html += '<ul class="ne-list">';

      networks.forEach(network => {
        html += '<li class="ne-item">';
        html += '<div class="ne-name">' + network.name + '</div>';
        html += '<div class="ne-details">';
        html += '<span><strong>地址:</strong> ' + network.ne_address + '</span>';
        html += '<span><strong>负责人:</strong> ' + network.owner + '</span>';
        html += '</div>';
        html += '</li>';
      });

      html += '</ul>';
      html += '</div>';

      // 第二区域：拓扑图
      html += '<div class="section topo-image">';
      html += '<h3 class="section-title">网络拓扑图</h3>';
      html += '<div class="topo-img-container">';

      const imagePath = 'ne_topo/' + topology + '.png';
      html += '<img class="topo-img" src="' + imagePath + '" alt="' + topology + ' topology" ';
      html += 'onerror="this.style.display=\'none\'; this.nextElementSibling.style.display=\'flex\'">';
      html += '<div class="no-image" style="display: none;">暂无拓扑图<br><small>(' + topology + '.png)</small></div>';

      html += '</div>';
      html += '</div>';

      // 第三区域：串口信息
      html += '<div class="section console-info">';
      html += '<h3 class="section-title">串口日志信息</h3>';
      html += generateConsoleTabSystem(networks, topology);
      html += '</div>';
      html += '</div>';

      return html;
    }

    // 生成网元串口组
    function generateNetworkConsoleGroup(network) {
      const consoleUrl = network.console_url || 'http://***************:7654';

      let html = '<div class="network-console-group">';
      html += '<div class="network-title">' + network.name + '</div>';
      html += '<div class="network-info">';
      html += '<div class="network-info-item">';
      html += '<span class="network-info-label">地址:</span>';
      html += '<span class="network-info-value">' + network.ne_address + '</span>';
      html += '</div>';
      html += '<div class="network-info-item">';
      html += '<span class="network-info-label">负责人:</span>';
      html += '<span class="network-info-value">' + network.owner + '</span>';
      html += '</div>';
      html += '<div class="network-info-item">';
      html += '<span class="network-info-label">服务器:</span>';
      html += '<span class="network-info-value">' + consoleUrl + '</span>';
      html += '</div>';
      html += '</div>';

      html += '<div class="console-slots">';
      network.console_trace.forEach(console => {
        html += generateConsoleSection(network, console);
      });
      html += '</div>';

      html += '</div>';
      return html;
    }

    // 生成串口区域
    function generateConsoleSection(network, console) {
      const consoleUrl = network.console_url || 'http://***************:7654';
      const slot = console.slot;
      const consoleName = console.console;

      let html = '<div class="console-slot">';
      html += '<div class="slot-title">Slot: ' + slot + '</div>';

      // 基本信息
      html += '<div class="slot-info">';
      html += '<div class="slot-info-item">';
      html += '<span class="slot-info-label">控制台:</span>';
      html += '<span class="slot-info-value">' + consoleName + '</span>';
      html += '</div>';
      html += '</div>';

      // 近五天日志链接
      html += '<div class="log-links-title" style="margin-bottom: 10px; font-weight: 600; color: #00ffff;">近五天日志:</div>';
      html += '<ul class="log-links">';

      for (let i = 0; i < 5; i++) {
        const date = new Date();
        date.setDate(date.getDate() - i);
        const dateStr = date.getFullYear() + '/' +
                       String(date.getMonth() + 1).padStart(2, '0') + '/' +
                       String(date.getDate()).padStart(2, '0');

        const logUrl = consoleUrl + '/' + dateStr + '/FSV/' + consoleName + '.txt';
        const displayDate = date.getFullYear() + '-' +
                           String(date.getMonth() + 1).padStart(2, '0') + '-' +
                           String(date.getDate()).padStart(2, '0');

        html += '<li>';
        html += '<a href="' + logUrl + '" class="log-link" target="_blank">';
        html += displayDate + ' - ' + consoleName + '.txt';
        html += '</a>';
        html += '</li>';
      }

      html += '</ul>';
      html += '</div>';

      return html;
    }

    // 切换Tab
    function switchTab(topology) {
      // 更新Tab状态
      document.querySelectorAll('.tab').forEach(tab => {
        tab.classList.remove('active');
        if (tab.dataset.topology === topology) {
          tab.classList.add('active');
        }
      });

      // 更新内容显示
      document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.remove('active');
        if (content.id === 'tab-' + topology) {
          content.classList.add('active');
        }
      });

      currentActiveTab = topology;
      console.log('切换到拓扑:', topology);
    }

    // 初始化
    document.addEventListener('DOMContentLoaded', function() {
      loadTopoConfig();
    });
  </script>
</body>
</html>
