{"moduleNames": {"mrStatus": "MR 状态监控", "performanceMonitoring": "性能监控系统", "testExecution": "测试执行中心", "testReports": "测试报告中心", "testResources": "测试资源管理", "testDocuments": "测试文档中心"}, "buttonNames": {"R1.0": "R1.0", "R1.2": "R1.2", "R1.3": "R1.3", "R1.4": "R1.4", "ne": "NE", "ne summary": "NE Summary", "cpuMemory": "CPU Memory", "cpuMemorySummary": "CPU Memory Summary", "hardDisk": "HardDisk", "slot": "Slot", "port": "Port", "alarm": "Alarm", "avatar": "Avatar", "goat": "Goa<PERSON>", "sanity": "Sanity", "avatarReport": "Avatar Report", "goatReport": "Goat Report", "sanityReport": "Sanity Report", "kunpeng issues": "KUNPENG Issues", "kunpeng network issues": "KUNPENG Network Issues", "codeManagement": "代码管理", "testEnvironment": "测试环境", "testTopology": "测试拓扑", "serialPortFSV": "串口管理FSV", "serialPortSW": "串口管理SW", "emsServer": "EMS Server", "productDoc": "产品文档", "requirementDoc": "需求文档", "confluence wiki page": "Confluence Wiki"}, "modules": {"mrStatus": {"R1.0": "", "R1.2": "", "R1.3": "", "R1.4": "kunpeng_readmine_issue_status_v2.html"}, "performanceMonitoring": {"ne": "http://100.120.180.251:5601/app/dashboards#/view/7bb2382a-f0e1-4e46-b05b-17c12402d85b?embed=true&_g=(refreshInterval%3A(pause%3A!t%2Cvalue%3A60000)%2Ctime%3A(from%3Anow-1w%2Cto%3Anow))&show-time-filter=true", "ne summary": "http://100.120.180.251:5601/app/dashboards#/view/7bb2382a-f0e1-4e46-b05b-17c12402d85b?embed=true&_g=(refreshInterval%3A(pause%3A!t%2Cvalue%3A60000)%2Ctime%3A(from%3Anow-1w%2Cto%3Anow))&show-query-input=true&show-time-filter=true", "cpuMemory": "http://100.120.180.251:5601/app/dashboards#/view/50f7ddce-8266-4f1e-9902-eb0eeaf3a401?embed=true&_g=(refreshInterval%3A(pause%3A!t%2Cvalue%3A60000)%2Ctime%3A(from%3Anow-1w%2Cto%3Anow))&show-query-input=true&show-time-filter=true", "cpuMemorySummary": "http://100.120.180.251:5601/app/dashboards#/view/53fe5349-1953-42ad-9917-dd20e03d01ba?embed=true&_g=(refreshInterval%3A(pause%3A!t%2Cvalue%3A60000)%2Ctime%3A(from%3Anow-5h%2Cto%3Anow))&show-query-input=true&show-time-filter=true", "hardDisk": "", "hardDiskSummary": "", "slot": "", "slotSummary": "", "port": "", "portSummary": "", "alarm": "", "alarmSummary": ""}, "testExecution": {"avatar": "http://100.120.182.16:8088/view/Avatar/", "goat": "http://100.120.182.16:8088/view/PSIS/", "sanity": "http://100.120.182.16:8088/view/PSIS_Sanity/"}, "testReports": {"avatarReport": "http://100.120.180.253:8080/ui/#avatar_demo/launches/all", "goatReport": "http://100.120.180.253:8080/ui/#kunpeng/launches/all", "sanityReport": "http://100.120.180.253:8080/ui/#avatar_demo/launches/67", "kunpeng issues": "https://kpmr.int.nokia-sbell.com/projects/kp/issues", "kunpeng network issues": "https://kpmr.int.nokia-sbell.com/projects/kp-network-automation/issues"}, "testResources": {"codeManagement": "http://100.120.182.21/jwang089/goat", "testEnvironment": "http://100.120.180.253:8080/ui/#kunpeng/launches/all", "testTopology": "", "serialPortFSV": "http://100.120.180.137:7654/consoles/production.xml", "serialPortSW": "http://100.120.180.196:7654/console/production.xml", "emsServer": "http://100.120.182.86:8002/login"}, "testDocuments": {"productDoc": "https://confluence.ext.net.nokia.com/pages/viewpage.action?pageId=1501188685", "requirementDoc": "https://confluence.ext.net.nokia.com/display/kpdci/KunPeng", "confluence wiki page": "https://confluence-app.ext.net.nokia.com/pages/viewpage.action?pageId=487330318"}}}