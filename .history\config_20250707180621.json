{"moduleNames": {"mrStatus": "MR状态监控", "performanceMonitoring": "性能监控", "testExecution": "测试执行", "testReports": "测试报告", "testResources": "测试资源", "testDocuments": "测试文档"}, "buttonNames": {"R1.0": "R1.0", "R1.2": "R1.2", "R1.3": "R1.3", "R1.4": "R1.4", "ne": "ne", "ne summary": "ne summary", "cpu": "cpu", "cpu summary": "cpu summary", "disk": "disk", "disk summary": "disk summary", "slot": "slot", "slot summary": "slot summary", "port": "port", "port summary": "port summary", "alarm": "alarm", "alarm summary": "alarm summary", "jenkins": "<PERSON>", "avatar": "Avatar", "goat": "Goa<PERSON>", "sanity": "Sanity", "avatarReport": "Avatar Report", "goatReport": "Goat Report", "sanityReport": "Sanity Report", "kunpeng issues": "KUNPENG Issues", "kunpeng network issues": "KUNPENG Network Issues", "codeManagement": "代码管理", "testTopology": "测试拓扑", "serialPortFSV": "串口管理FSV", "serialPortSW": "串口管理SW", "emsServer": "EMS Server", "productDoc": "产品文档", "requirementDoc": "需求文档", "confluence wiki page": "Confluence Wiki"}, "modules": {"mrStatus": {"R1.0": "", "R1.2": "", "R1.3": "", "R1.4": "kunpeng_readmine_issue_status_v2.html"}, "performanceMonitoring": {"ne": "http://100.120.180.251:5601/app/dashboards#/view/7bb2382a-f0e1-4e46-b05b-17c12402d85b?embed=true&_g=(refreshInterval%3A(pause%3A!t%2Cvalue%3A60000)%2Ctime%3A(from%3Anow-1w%2Cto%3Anow))&show-time-filter=true", "ne summary": "http://100.120.180.251:5601/app/dashboards#/view/53fe5349-1953-42ad-9917-dd20e03d01ba?embed=true&_g=(refreshInterval%3A(pause%3A!t%2Cvalue%3A60000)%2Ctime%3A(from%3Anow-5h%2Cto%3Anow))&show-query-input=true&show-time-filter=true", "cpu ": "http://100.120.180.251:5601/app/dashboards#/view/50f7ddce-8266-4f1e-9902-eb0eeaf3a401?embed=true&_g=(refreshInterval%3A(pause%3A!t%2Cvalue%3A60000)%2Ctime%3A(from%3Anow-1w%2Cto%3Anow))&show-query-input=true&show-time-filter=true", "cpu summary": "http://100.120.180.251:5601/app/dashboards#/view/c1c8b1b3-9ad8-4c20-9ada-d1537c805679?embed=true&_g=(refreshInterval%3A(pause%3A!t%2Cvalue%3A60000)%2Ctime%3A(from%3Anow-5h%2Cto%3Anow))&show-query-input=true&show-time-filter=true", "disk": "http://100.120.180.251:5601/app/dashboards#/view/3c349dd0-9eda-4260-8a64-26ae6e92754d?embed=true&_g=(refreshInterval%3A(pause%3A!t%2Cvalue%3A60000)%2Ctime%3A(from%3Anow-1w%2Cto%3Anow))&show-query-input=true&show-time-filter=true", "disk summary": "http://100.120.180.251:5601/app/dashboards#/view/fdf8b549-e434-42be-ace5-3066e186c3f4?embed=true&_g=(refreshInterval%3A(pause%3A!t%2Cvalue%3A60000)%2Ctime%3A(from%3Anow-5h%2Cto%3Anow))&show-query-input=true&show-time-filter=true", "slot": "http://100.120.180.251:5601/app/dashboards#/view/82c37a1f-51a4-4a72-8472-0bdc74622df5?embed=true&_g=(refreshInterval%3A(pause%3A!t%2Cvalue%3A60000)%2Ctime%3A(from%3Anow-1w%2Cto%3Anow))&show-query-input=true&show-time-filter=true", "slot summary": "http://100.120.180.251:5601/app/dashboards#/view/88c5fdd7-c484-4853-9a12-a9c12b682c4a?embed=true&_g=(refreshInterval%3A(pause%3A!t%2Cvalue%3A60000)%2Ctime%3A(from%3Anow-5h%2Cto%3Anow))&show-query-input=true&show-time-filter=true", "port": "http://100.120.180.251:5601/app/dashboards#/view/c125a83c-0466-4624-8cdd-81bc693fa402?embed=true&_g=(refreshInterval%3A(pause%3A!t%2Cvalue%3A60000)%2Ctime%3A(from%3Anow-1w%2Cto%3Anow))&show-query-input=true&show-time-filter=true", "port summary": "http://100.120.180.251:5601/app/dashboards#/view/14ed72a4-00b6-4740-911d-ce1942488a6f?embed=true&_g=(refreshInterval%3A(pause%3A!t%2Cvalue%3A60000)%2Ctime%3A(from%3Anow-5h%2Cto%3Anow))&show-query-input=true&show-time-filter=true", "alarm": "http://100.120.180.251:5601/app/dashboards#/view/002dfb99-0d65-4653-a6ff-f683f2e2b81f?embed=true&_g=(refreshInterval%3A(pause%3A!t%2Cvalue%3A60000)%2Ctime%3A(from%3Anow-1w%2Cto%3Anow))&show-query-input=true&show-time-filter=true", "alarm summary": "http://100.120.180.251:5601/app/dashboards#/view/53767f7b-b58e-402d-a718-2bd59f46afd3?embed=true&_g=(refreshInterval%3A(pause%3A!t%2Cvalue%3A60000)%2Ctime%3A(from%3Anow-5h%2Cto%3Anow))&show-query-input=true&show-time-filter=true"}, "testExecution": {"jenkins": "http://100.120.182.16:8088/view/PSIS/", "avatar": "http://100.120.182.16:8088/view/Avatar/", "goat": "http://100.120.182.16:8088/view/PSIS/", "sanity": "http://100.120.182.16:8088/view/PSIS_Sanity/"}, "testReports": {"avatarReport": "http://100.120.180.253:8080/ui/#avatar_demo/launches/all", "goatReport": "http://100.120.180.253:8080/ui/#kunpeng/launches/all", "sanityReport": "http://100.120.180.253:8080/ui/#avatar_demo/launches/67", "kunpeng issues": "https://kpmr.int.nokia-sbell.com/projects/kp/issues", "kunpeng network issues": "https://kpmr.int.nokia-sbell.com/projects/kp-network-automation/issues"}, "testResources": {"codeManagement": "http://100.120.182.21/jwang089/goat", "testTopology": "NE_topology.html", "serialPortFSV": "http://100.120.180.137:7654/consoles/production.xml", "serialPortSW": "http://100.120.180.196:7654/console/production.xml", "emsServer": "http://100.120.182.86:8002/login"}, "testDocuments": {"productDoc": "https://confluence.ext.net.nokia.com/pages/viewpage.action?pageId=1501188685", "requirementDoc": "https://confluence.ext.net.nokia.com/display/kpdci/KunPeng", "confluence wiki page": "https://confluence-app.ext.net.nokia.com/pages/viewpage.action?pageId=487330318"}}}