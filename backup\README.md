# KUNPENG Test Portal - Backup Files

这个目录包含了开发过程中的历史版本和测试文件。

## 📁 文件说明

### **历史版本文件**
- `kunpeng_test_portal.html` - 最初版本的主页面
- `kunpeng_test_portal_fixed.html` - 修复版本
- `kunpeng_portal_stable.html` - 稳定版本
- `kunpeng_dynamic_portal.html` - 动态配置版本

### **测试文件**
- `debug_test.html` - 调试测试页面
- `config_fixed.json` - 修复后的配置文件副本

## 🎯 当前生产文件

主目录中的生产文件：
- `kunpeng_portal_final.html` - **最终生产版本**
- `config.json` - **当前配置文件**
- `kunpeng_readmine_issue_status_v2.html` - R1.4页面
- `start_kunpeng_portal.bat` - Windows启动脚本
- `start_kunpeng_portal.ps1` - PowerShell启动脚本

## 📝 版本历史

1. **kunpeng_test_portal.html** - 初始版本，基础功能
2. **kunpeng_test_portal_fixed.html** - 修复JavaScript错误
3. **kunpeng_portal_stable.html** - 稳定版本，简化代码
4. **kunpeng_dynamic_portal.html** - 动态配置系统
5. **kunpeng_portal_final.html** - 最终版本，包含所有功能

## ⚠️ 注意事项

- 这些文件仅作为备份和参考
- 不建议在生产环境中使用这些历史版本
- 如需回滚，请谨慎测试后再使用

## 🗑️ 清理建议

如果确认不再需要这些备份文件，可以安全删除此目录。
