<!DOCTYPE html>
<html lang="zh">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>KUNPENG Test Portal</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
      background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
      color: #ffffff;
      min-height: 100vh;
      overflow-x: hidden;
    }

    /* 科技感背景动画 */
    .tech-bg {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: -1;
      opacity: 0.1;
    }

    .tech-grid {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-image: 
        linear-gradient(rgba(0, 255, 255, 0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(0, 255, 255, 0.1) 1px, transparent 1px);
      background-size: 50px 50px;
      animation: gridMove 20s linear infinite;
    }

    @keyframes gridMove {
      0% { transform: translate(0, 0); }
      100% { transform: translate(50px, 50px); }
    }

    /* 头部标题 */
    .header {
      text-align: center;
      padding: 40px 20px;
      background: linear-gradient(90deg, rgba(0, 255, 255, 0.1), rgba(0, 122, 204, 0.1));
      border-bottom: 2px solid rgba(0, 255, 255, 0.3);
      position: relative;
    }

    .header::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 2px;
      background: linear-gradient(90deg, transparent, #00ffff, transparent);
      animation: scanLine 3s ease-in-out infinite;
    }

    @keyframes scanLine {
      0%, 100% { opacity: 0; }
      50% { opacity: 1; }
    }

    .header h1 {
      font-size: 4.5rem;
      font-weight: 700;
      letter-spacing: 5px;
      background: linear-gradient(45deg, #00ffff, #0099ff, #00ffff, #66ccff);
      background-size: 400% 400%;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      animation: gradientShift 3s ease-in-out infinite, textGlow 2s ease-in-out infinite alternate;
      margin-bottom: 15px;
      position: relative;
    }

    .header h1::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(45deg, #00ffff, #0099ff, #00ffff, #66ccff);
      background-size: 400% 400%;
      animation: gradientShift 3s ease-in-out infinite;
      filter: blur(20px);
      opacity: 0.3;
      z-index: -1;
    }

    @keyframes gradientShift {
      0%, 100% { background-position: 0% 50%; }
      50% { background-position: 100% 50%; }
    }

    @keyframes textGlow {
      0% { filter: drop-shadow(0 0 10px rgba(0, 255, 255, 0.5)); }
      100% { filter: drop-shadow(0 0 30px rgba(0, 255, 255, 0.8)) drop-shadow(0 0 40px rgba(0, 153, 255, 0.6)); }
    }

    .header .subtitle {
      font-size: 1.8rem;
      font-weight: 500;
      background: linear-gradient(90deg, #ffffff, #00ffff, #ffffff);
      background-size: 200% 200%;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      animation: subtitleGlow 4s ease-in-out infinite;
      letter-spacing: 2px;
      text-shadow: 0 0 15px rgba(255, 255, 255, 0.3);
    }

    @keyframes subtitleGlow {
      0%, 100% { background-position: 0% 50%; }
      50% { background-position: 100% 50%; }
    }

    /* 主要内容区域 */
    .main-container {
      max-width: 1800px;
      margin: 0 auto;
      padding: 40px 20px;
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 25px;
    }

    @media (max-width: 1200px) {
      .main-container {
        grid-template-columns: repeat(2, 1fr);
      }
    }

    /* 模块卡片 */
    .module-card {
      background: linear-gradient(145deg, rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.02));
      border: 1px solid rgba(0, 255, 255, 0.2);
      border-radius: 15px;
      padding: 25px;
      backdrop-filter: blur(10px);
      position: relative;
      transition: all 0.3s ease;
      overflow: hidden;
    }

    .module-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(0, 255, 255, 0.1), transparent);
      transition: left 0.5s ease;
    }

    .module-card:hover::before {
      left: 100%;
    }

    .module-card:hover {
      transform: translateY(-5px);
      border-color: rgba(0, 255, 255, 0.5);
      box-shadow: 0 10px 30px rgba(0, 255, 255, 0.2);
    }

    .module-title {
      font-size: 1.5rem;
      font-weight: 600;
      margin-bottom: 20px;
      color: #00ffff;
      text-align: center;
      position: relative;
    }

    .module-title::after {
      content: '';
      position: absolute;
      bottom: -5px;
      left: 50%;
      transform: translateX(-50%);
      width: 50px;
      height: 2px;
      background: linear-gradient(90deg, transparent, #00ffff, transparent);
    }

    /* 按钮样式 */
    .btn-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
      gap: 15px;
      margin-top: 20px;
    }

    .tech-btn {
      background: linear-gradient(45deg, rgba(0, 122, 204, 0.2), rgba(0, 255, 255, 0.1));
      border: 1px solid rgba(0, 255, 255, 0.3);
      color: #ffffff;
      padding: 12px 20px;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.3s ease;
      text-decoration: none;
      display: block;
      text-align: center;
      font-size: 0.9rem;
      position: relative;
      overflow: hidden;
    }

    .tech-btn::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      transition: left 0.3s ease;
    }

    .tech-btn:hover::before {
      left: 100%;
    }

    .tech-btn:hover {
      background: linear-gradient(45deg, rgba(0, 122, 204, 0.4), rgba(0, 255, 255, 0.2));
      border-color: rgba(0, 255, 255, 0.6);
      transform: translateY(-2px);
      box-shadow: 0 5px 15px rgba(0, 255, 255, 0.3);
    }

    /* 状态指示器 */
    .status-indicator {
      display: inline-block;
      width: 8px;
      height: 8px;
      border-radius: 50%;
      margin-right: 8px;
    }

    .status-active {
      background: #00ff00;
      box-shadow: 0 0 10px rgba(0, 255, 0, 0.5);
      animation: pulse 2s infinite;
    }

    .status-inactive {
      background: #ff6b6b;
      box-shadow: 0 0 10px rgba(255, 107, 107, 0.5);
    }

    @keyframes pulse {
      0%, 100% { opacity: 1; }
      50% { opacity: 0.5; }
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
      .header h1 {
        font-size: 3rem;
        letter-spacing: 3px;
      }
      
      .header .subtitle {
        font-size: 1.4rem;
        letter-spacing: 1px;
      }
      
      .main-container {
        grid-template-columns: 1fr;
        padding: 20px 10px;
      }
      
      .btn-grid {
        grid-template-columns: 1fr;
      }
    }

    /* 模态框样式 */
    .modal {
      display: none;
      position: fixed;
      z-index: 1000;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.8);
      backdrop-filter: blur(5px);
    }

    .modal-content {
      background: linear-gradient(145deg, rgba(26, 26, 46, 0.95), rgba(22, 33, 62, 0.95));
      margin: 5% auto;
      padding: 30px;
      border: 1px solid rgba(0, 255, 255, 0.3);
      border-radius: 15px;
      width: 90%;
      max-width: 500px;
      text-align: center;
      position: relative;
    }

    .close {
      color: #aaa;
      float: right;
      font-size: 28px;
      font-weight: bold;
      position: absolute;
      right: 15px;
      top: 10px;
      cursor: pointer;
    }

    .close:hover {
      color: #00ffff;
    }

    /* 加载动画 */
    .loading {
      text-align: center;
      padding: 50px;
      color: #00ffff;
      font-size: 1.2rem;
    }

    .loading::after {
      content: '';
      display: inline-block;
      width: 20px;
      height: 20px;
      border: 2px solid rgba(0, 255, 255, 0.3);
      border-top: 2px solid #00ffff;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-left: 10px;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  </style>
</head>
<body>
  <div class="tech-bg">
    <div class="tech-grid"></div>
  </div>

  <div class="header">
    <h1>KUNPENG Test Portal</h1>
    <div class="subtitle">Advanced Testing & Monitoring Dashboard</div>
  </div>

  <div class="main-container" id="mainContainer">
    <div class="loading">加载配置中...</div>
  </div>

  <!-- 模态框 -->
  <div id="errorModal" class="modal">
    <div class="modal-content">
      <span class="close">&times;</span>
      <h3 style="color: #ff6b6b; margin-bottom: 20px;">页面不存在</h3>
      <p>抱歉，该功能页面暂未配置或不存在。</p>
      <p style="margin-top: 15px; color: rgba(255,255,255,0.7);">请联系管理员进行配置。</p>
    </div>
  </div>

  <script>
    let config = {};
    let moduleNames = {};
    let buttonNames = {};

    // 加载配置文件
    async function loadConfig() {
      try {
        const response = await fetch('config.json');
        const configData = await response.json();

        // 从JSON中提取配置
        moduleNames = configData.moduleNames || {};
        buttonNames = configData.buttonNames || {};
        config = configData.modules || configData; // 兼容旧格式

        console.log('配置加载成功:', {moduleNames, buttonNames, config});
        generateDynamicContent();
      } catch (error) {
        console.error('配置加载失败:', error);
        document.getElementById('mainContainer').innerHTML = '<div class="loading">配置加载失败，请检查config.json文件</div>';
      }
    }

    // 动态生成页面内容
    function generateDynamicContent() {
      const container = document.getElementById('mainContainer');
      container.innerHTML = '';

      Object.keys(config).forEach(moduleKey => {
        const moduleData = config[moduleKey];
        const moduleTitle = moduleNames[moduleKey] || moduleKey;

        // 创建模块卡片
        const moduleCard = document.createElement('div');
        moduleCard.className = 'module-card';

        // 创建模块标题
        const titleElement = document.createElement('h2');
        titleElement.className = 'module-title';
        titleElement.textContent = moduleTitle;

        // 创建按钮网格
        const btnGrid = document.createElement('div');
        btnGrid.className = 'btn-grid';

        // 为每个按钮创建元素
        Object.keys(moduleData).forEach(buttonKey => {
          const buttonUrl = moduleData[buttonKey];
          const buttonName = buttonNames[buttonKey] || buttonKey;

          const button = document.createElement('a');
          button.href = '#';
          button.className = 'tech-btn';
          button.dataset.module = moduleKey;
          button.dataset.key = buttonKey;

          const statusIndicator = document.createElement('span');
          statusIndicator.className = 'status-indicator';
          statusIndicator.classList.add(buttonUrl && buttonUrl.trim() !== '' ? 'status-active' : 'status-inactive');

          button.appendChild(statusIndicator);
          button.appendChild(document.createTextNode(buttonName));

          // 绑定点击事件
          button.addEventListener('click', handleButtonClick);

          btnGrid.appendChild(button);
        });

        moduleCard.appendChild(titleElement);
        moduleCard.appendChild(btnGrid);
        container.appendChild(moduleCard);
      });

      // 绑定模态框事件
      bindModalEvents();
    }

    // 智能判断是否为iframe链接
    function isIframeUrl(url) {
      const iframeIndicators = [
        'embed=true',
        'iframe',
        '/dashboards#/view/',
        '/app/dashboards',
        'kibana',
        'grafana'
      ];

      return iframeIndicators.some(indicator =>
        url.toLowerCase().includes(indicator.toLowerCase())
      );
    }

    // 处理按钮点击
    function handleButtonClick(event) {
      event.preventDefault();

      const button = event.currentTarget;
      const module = button.dataset.module;
      const key = button.dataset.key;

      console.log('点击按钮:', module, key);

      if (!config[module] || !config[module][key]) {
        showErrorModal();
        return;
      }

      const url = config[module][key];
      if (!url || url.trim() === '') {
        showErrorModal();
        return;
      }

      console.log('URL:', url);

      // 智能判断链接类型
      if (isIframeUrl(url)) {
        // iframe链接，创建科技感仪表板页面
        createSimpleTechDashboard(url, buttonNames[key] || key);
      } else if (url.endsWith('.html')) {
        // HTML文件直接在新窗口打开
        console.log('打开HTML文件:', url);
        window.open(url, '_blank');
      } else {
        // 其他链接直接跳转
        console.log('打开其他链接:', url);
        window.open(url, '_blank');
      }
    }

    // 显示错误模态框
    function showErrorModal() {
      document.getElementById('errorModal').style.display = 'block';
    }

    // 关闭模态框
    function closeModal() {
      document.getElementById('errorModal').style.display = 'none';
    }

    // 绑定模态框事件
    function bindModalEvents() {
      const closeBtn = document.querySelector('.close');
      if (closeBtn) {
        closeBtn.addEventListener('click', closeModal);
      }

      window.addEventListener('click', function(event) {
        const modal = document.getElementById('errorModal');
        if (event.target === modal) {
          closeModal();
        }
      });
    }

    // 创建简化的科技感仪表板页面
    function createSimpleTechDashboard(url, title) {
      const newWindow = window.open('', '_blank');

      // 分步骤写入HTML，避免复杂字符串拼接
      newWindow.document.write('<!DOCTYPE html>');
      newWindow.document.write('<html lang="zh">');
      newWindow.document.write('<head>');
      newWindow.document.write('<meta charset="UTF-8">');
      newWindow.document.write('<title>KUNPENG Tech Dashboard - ' + title + '</title>');

      // CSS样式
      newWindow.document.write('<style>');
      newWindow.document.write('html,body{margin:0;padding:0;height:100%;overflow:hidden;font-family:"Segoe UI",sans-serif}');
      newWindow.document.write('body{background:linear-gradient(135deg,#0a0a0a,#1a1a2e,#16213e);color:#fff}');
      newWindow.document.write('.tech-bg{position:fixed;top:0;left:0;width:100%;height:100%;z-index:-1;opacity:0.1}');
      newWindow.document.write('.tech-grid{position:absolute;top:0;left:0;width:100%;height:100%;background-image:linear-gradient(rgba(0,255,255,0.1) 1px,transparent 1px),linear-gradient(90deg,rgba(0,255,255,0.1) 1px,transparent 1px);background-size:50px 50px;animation:gridMove 20s linear infinite}');
      newWindow.document.write('@keyframes gridMove{0%{transform:translate(0,0)}100%{transform:translate(50px,50px)}}');
      newWindow.document.write('.tabs{display:flex;background:linear-gradient(90deg,rgba(0,255,255,0.1),rgba(0,122,204,0.2));padding:10px;border-bottom:2px solid rgba(0,255,255,0.3)}');
      newWindow.document.write('.tab{padding:10px 24px;background:linear-gradient(90deg,#007acc,#3399ff);border:none;border-radius:12px 12px 0 0;font-weight:600;color:#fff}');
      newWindow.document.write('.content{padding:10px;height:calc(100vh - 80px)}');
      newWindow.document.write('.dashboard-box{border:2px solid rgba(0,255,255,0.3);border-radius:15px;background:rgba(255,255,255,0.05);height:100%;overflow:hidden}');
      newWindow.document.write('.dashboard-header{padding:15px;background:rgba(0,122,204,0.3);color:#00ffff;font-weight:bold}');
      newWindow.document.write('iframe{width:100%;height:calc(100% - 60px);border:none}');
      newWindow.document.write('</style>');

      newWindow.document.write('</head>');
      newWindow.document.write('<body>');

      // 背景
      newWindow.document.write('<div class="tech-bg"><div class="tech-grid"></div></div>');

      // 标签栏
      newWindow.document.write('<div class="tabs">');
      newWindow.document.write('<button class="tab">KUNPENG Tech Dashboard - ' + title + '</button>');
      newWindow.document.write('</div>');

      // 内容
      newWindow.document.write('<div class="content">');
      newWindow.document.write('<div class="dashboard-box">');
      newWindow.document.write('<div class="dashboard-header">' + title + ' - 实时监控面板</div>');
      newWindow.document.write('<iframe src="' + url + '"></iframe>');
      newWindow.document.write('</div>');
      newWindow.document.write('</div>');

      newWindow.document.write('</body>');
      newWindow.document.write('</html>');

      newWindow.document.close();
    }

    // 初始化
    document.addEventListener('DOMContentLoaded', function() {
      loadConfig();
    });
  </script>
</body>
</html>
