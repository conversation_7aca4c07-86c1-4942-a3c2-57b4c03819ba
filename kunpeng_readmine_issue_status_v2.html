<!DOCTYPE html>
<html lang="zh">
<head>
  <meta charset="UTF-8">
  <title>KUNPENG Project Dashboards</title>
  <style>
    html, body {
      margin: 0;
      padding: 0;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background: linear-gradient(135deg, #f0f4f8, #ffffff);
    }

    .tabs {
      display: flex;
      background-color: #ffffff;
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
      padding: 10px;
    }

    .tab {
      padding: 10px 24px;
      margin-right: 10px;
      background: linear-gradient(#e0e0e0, #dcdcdc);
      border: none;
      border-radius: 12px 12px 0 0;
      cursor: pointer;
      font-weight: 600;
      transition: background 0.3s, transform 0.2s, color 0.3s;
    }

    .tab:hover {
      background: #cccccc;
      transform: scale(1.05);
    }

    .tab.active {
      background: linear-gradient(90deg, #007acc, #3399ff);
      color: #ffffff;
      box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
    }

    .content {
      padding: 10px;
      box-sizing: border-box;
    }

    .dashboard-container {
      display: flex;
      flex-direction: column;
      gap: 20px;
    }

    .dashboard-box {
      border: 2px solid #007acc;
      border-radius: 10px;
      background-color: #fff;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      overflow: hidden;
      box-sizing: border-box; /* Include borders in element's total width/height */
    }

    /* Set specific heights for each iframe box */
    #report1-container {
      height: 1005px;
    }

    #report2-container {
      height: 2700px;
    }

    .dashboard-header {
      padding: 10px;
      font-size: 16px;
      font-weight: bold;
      color: #ffffff;
      background-color: #007acc;
      border-bottom: 1px solid #007acc;
    }

    iframe {
      width: 100%;
      border: none;
      display: block;
      height: 100%; /* Ensure iframe takes full height of the container */
    }

    #loading {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      font-size: 18px;
      background: rgba(255, 255, 255, 0.8);
      padding: 20px 30px;
      border-radius: 10px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      display: none;
    }
  </style>
</head>
<body>
  <div class="tabs">
    <button class="tab active" onclick="switchTab(0)">KUNPENG Dashboards</button>
  </div>

  <div class="content">
    <div id="loading">加载中，请稍候...</div>

    <div class="dashboard-container">
      <div class="dashboard-box" id="report1-container">
        <div class="dashboard-header">KUNPENG 趋势统计报告</div>
        <iframe
          id="report1"
          #src="http://***************:5601/app/dashboards#/view/038a9f73-0060-4117-8e9c-97fb61d36556?embed=true&_g=(refreshInterval:(pause:!f,value:14400000),time:(from:now-7w,to:now))&show-time-filter=true&hide-filter-bar=true"
          src="http://***************:5601/app/dashboards#/view/038a9f73-0060-4117-8e9c-97fb61d36556?embed=true&_g=(refreshInterval%3A(pause%3A!f%2Cvalue%3A14400000)%2Ctime%3A(from%3Anow-4M%2Cto%3Anow))&show-time-filter=true&hide-filter-bar=true"
		  
		  onload="resizeIframe(this)">
        </iframe>
      </div>

      <div class="dashboard-box" id="report2-container">
        <div class="dashboard-header">KUNPENG 每日问题报告</div>
        <iframe
          id="report2"
          #src="http://***************:5601/app/dashboards#/view/78c24426-b933-4b2b-8f07-67774938dba3?embed=true&_g=(refreshInterval:(pause:!f,value:14400000),time:(from:now-6d,to:now))&show-time-filter=true&hide-filter-bar=true"
          src="http://***************:5601/app/dashboards#/view/78c24426-b933-4b2b-8f07-67774938dba3?embed=true&_g=()&hide-filter-bar=true"
		  onload="resizeIframe(this)">
        </iframe>
      </div>
    </div>
  </div>

  <script>
    function switchTab(index) {
      const tabs = document.querySelectorAll('.tab');
      const loader = document.getElementById('loading');

      loader.style.display = 'block';

      tabs.forEach((tab, i) => {
        tab.classList.toggle('active', i === index);
      });

      setTimeout(() => {
        loader.style.display = 'none';
      }, 800);
    }

    function resizeIframe(iframe) {
      try {
        const doc = iframe.contentDocument || iframe.contentWindow.document;
        const height = doc.documentElement.scrollHeight;
        iframe.style.height = height + 'px';
      } catch (err) {
        console.warn("无法调整 iframe 高度，可能存在跨域限制：", err);
      }
    }
  </script>
</body>
</html>
