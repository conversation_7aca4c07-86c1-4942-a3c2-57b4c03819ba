写一个KUNPENG Test Portal的主页面

里面分多个块
1 MR状态
  R1.0 对应链接从json文件读取，默认为空，提示用户页面不存在
  R1.2 对应链接从json文件读取，默认为空，提示用户页面不存在
  R1.3 对应链接从json文件读取，默认为空，提示用户页面不存在
  R1.4 点击R1.4可以到达kunpeng_readmine_issue_status_v2.html页面
2 性能监控
  NE Summary 对应链接<iframe src="http://100.120.180.251:5601/app/dashboards#/view/53fe5349-1953-42ad-9917-dd20e03d01ba?embed=true&_g=(refreshInterval%3A(pause%3A!t%2Cvalue%3A60000)%2Ctime%3A(from%3Anow-5h%2Cto%3Anow))&show-query-input=true&show-time-filter=true" height="600" width="800"></iframe>
  CPU Memory 对应链接
  HardDisk   对应链接
  Slot 对应链接从json文件读取，默认为空，提示用户页面不存在
  Port 对应链接从json文件读取，默认为空，提示用户页面不存在
  Alarm 对应链接从json文件读取，默认为空，提示用户页面不存在
  
3 测试报告
  Avatar Report 对应链接从json文件读取，默认为空，提示用户页面不存在
  Goat Report 对应链接从json文件读取，默认为空，提示用户页面不存在
  
