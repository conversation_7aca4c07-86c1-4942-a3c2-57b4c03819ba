# example.yaml
Topology:
- topology: topology_traffic:

  - name: setup_113
    ne_address: ***************
    owner:      WJF
    console_url: "http://***************:7654"
    console_trace:
        - slot: EC11
          console: 162.11_P5
        - slot: EC12
          console: 162.12_P9
        - slot: LC
          console: 162.12_P8
    slot_trace:
      - slot: 1
      - slot: 5
      - slot: 4
      - slot: 7
      - slot: 8
  - name: setup_106
    ne_address: ***************
    owner: WJF
    console_url: "http://***************:7654"
    console_trace:
      - slot: EC11
        console: 162.19_P1
      - slot: LC
        console: 162.19_P16
    slot_trace:
      - slot: 1
      - slot: 3
      - slot: 5
      - slot: 8
 - topology: topology_alarm:
  - name: setup_207
    ne_address: ***************
    owner: QZG
    console_trace:
      - slot: EC11
        console: 208.12_P13
      - slot: EC12
        console: 208.12_P10
      - slot: LC
        console: 208.19_P15
    slot_trace:
      - slot: 3
      - slot: 5
      - slot: 7
      - slot: 8
  - name: setup_211
    ne_address: ***************
    owner: MLT
    console_trace:
      - slot: EC11
        console: 208.12_P15
      - slot: EC12
        console: 208.12_P16
    slot_trace:
      - slot: 1
      - slot: 3
      - slot: 5
  - name: setup_221
    ne_address: ***************
    owner:      QZG
    console_trace:
        - slot: EC11
          console: 208.11_P8
        - slot: EC12
          console: 208.11_P2
    slot_trace:
      - slot: 1
      - slot: 3
      - slot: 5
      - slot: 7
 - topology: topology_sanity
  - name: setup_229
    ne_address: ***************
    owner:      SANITY
    console_url: "http://***************:7654"
    console_trace:
        - slot: EC11
          console: 162.18_P2
        - slot: EC12
          console: 162.18_P7
    slot_trace:
      - slot: 1
      - slot: 3
  - name: setup_236
    ne_address: ***************
    owner:      SANITY
    console_url: "http://***************:7654"
    console_trace:
        - slot: EC11
          console: 162.17_P16
        - slot: EC12
          console: 162.18_P1
    slot_trace:
      - slot: 4
      - slot: 5
      
monitor_parameter:
   ne: "Y"
   slot: "Y"
   port: "Y"
   transceiver: "Y"
   och: "Y"
   entity: "Y"
   alarm: "Y"
   console_trace: "Y"
   slot_trace: "Y"
   push_to_remote: "Y"
   
netconf_parameter:
    port: "5122"
    username: root
    password: Nokia#123

cli_parameter:
    port: "5122"
    username: root
    password: Nokia#123

ssh_parameter:
    port: "5122"
    username: root
    password: Nokia#123


      

