{"Topology_Group": [{"topology": "traffic", "networks": [{"name": "setup_113", "ne_address": "***************", "owner": "WJF", "console_url": "http://***************:7654", "console_trace": [{"slot": "EC11", "console": "162.11_P5"}, {"slot": "EC12", "console": "162.12_P9"}, {"slot": "LC", "console": "162.12_P8"}], "slot_trace": [{"slot": "1"}, {"slot": "5"}, {"slot": "4"}, {"slot": "7"}, {"slot": "8"}]}, {"name": "setup_106", "ne_address": "***************", "owner": "WJF", "console_url": "http://***************:7654", "console_trace": [{"slot": "EC11", "console": "162.19_P1"}, {"slot": "LC", "console": "162.19_P16"}], "slot_trace": [{"slot": "1"}, {"slot": "3"}, {"slot": "5"}, {"slot": "8"}]}]}, {"topology": "alarm", "networks": [{"name": "setup_111", "ne_address": "***************", "owner": "MLT", "console_url": "http://***************:7654", "console_trace": [{"slot": "EC11", "console": "162.12_P15"}, {"slot": "EC12", "console": "162.12_P16"}], "slot_trace": [{"slot": "1"}, {"slot": "3"}, {"slot": "5"}]}]}, {"topology": "IOP", "networks": [{"name": "setup_207", "ne_address": "***************", "owner": "QZG", "console_url": "http://***************:7654", "console_trace": [{"slot": "EC11", "console": "208.12_P13"}, {"slot": "EC12", "console": "208.12_P10"}, {"slot": "LC", "console": "208.19_P15"}], "slot_trace": [{"slot": "3"}, {"slot": "5"}, {"slot": "7"}, {"slot": "8"}]}, {"name": "setup_221", "ne_address": "***************", "owner": "QZG", "console_url": "http://***************:7654", "console_trace": [{"slot": "EC11", "console": "208.11_P8"}, {"slot": "EC12", "console": "208.11_P2"}], "slot_trace": [{"slot": "1"}, {"slot": "3"}, {"slot": "5"}, {"slot": "7"}]}]}, {"topology": "sanity", "networks": [{"name": "sanity_229", "ne_address": "***************", "owner": "SANITY", "console_url": "http://***************:7654", "console_trace": [{"slot": "EC11", "console": "162.18_P2"}, {"slot": "EC12", "console": "162.18_P7"}], "slot_trace": [{"slot": "1"}, {"slot": "3"}]}, {"name": "sanity_236", "ne_address": "***************", "owner": "SANITY", "console_url": "http://***************:7654", "console_trace": [{"slot": "EC11", "console": "162.17_P16"}, {"slot": "EC12", "console": "162.18_P1"}], "slot_trace": [{"slot": "4"}, {"slot": "5"}]}]}, {"topology": "tcas", "networks": [{"name": "setup_117", "ne_address": "***************", "owner": "ZM", "console_url": "http://***************:7654", "console_trace": [{"slot": "EC11", "console": "162.11_P8"}, {"slot": "EC12", "console": "162.18_P7"}], "slot_trace": [{"slot": "1"}, {"slot": "3"}]}, {"name": "setup_119", "ne_address": "***************", "owner": "ZM", "console_url": "http://***************:7654", "console_trace": [{"slot": "EC11", "console": "162.11_P2"}, {"slot": "EC12", "console": "162.12_P10"}], "slot_trace": [{"slot": "4"}, {"slot": "5"}]}]}, {"topology": "Loopback_DM", "networks": [{"name": "setup_120", "ne_address": "***************", "owner": "LMZ", "console_url": "http://***************:7654", "console_trace": [{"slot": "EC11", "console": "162.12_P11"}], "slot_trace": [{"slot": "1"}, {"slot": "3"}]}, {"name": "setup_101", "ne_address": "***************", "owner": "LMZ", "console_url": "http://***************:7654", "console_trace": [{"slot": "EC12", "console": "162.19_P2"}], "slot_trace": [{"slot": "4"}, {"slot": "5"}]}]}, {"topology": "olp_switch", "networks": [{"name": "setup_201", "ne_address": "***************", "owner": "WDC", "console_url": "http://***************:7654", "console_trace": [{"slot": "EC11", "console": "166.208_P2"}], "slot_trace": [{"slot": "1"}, {"slot": "3"}]}]}, {"topology": "bgp", "networks": [{"name": "setup_114", "ne_address": "***************", "owner": "WX", "console_url": "http://***************:7654", "console_trace": [{"slot": "EC11", "console": "162.12_P6"}, {"slot": "EC12", "console": "162.12_P12"}], "slot_trace": [{"slot": "1"}, {"slot": "3"}]}, {"name": "setup_115", "ne_address": "***************", "owner": "WX", "console_url": "http://***************:7654", "console_trace": [{"slot": "EC11", "console": "162.11_P7"}], "slot_trace": [{"slot": "4"}, {"slot": "5"}]}, {"name": "setup_123", "ne_address": "***************", "owner": "WX", "console_url": "http://***************:7654", "console_trace": [{"slot": "EC12", "console": "162.12_P4"}], "slot_trace": [{"slot": "4"}, {"slot": "5"}]}]}, {"topology": "pm", "networks": [{"name": "setup_116", "ne_address": "***************", "owner": "YJ", "console_url": "http://***************:7654", "console_trace": [{"slot": "EC11", "console": "162.19_P5"}, {"slot": "EC11", "console": "162.12_P13"}], "slot_trace": [{"slot": "1"}, {"slot": "3"}, {"slot": "5"}]}]}, {"topology": "manual", "networks": [{"name": "setup_111", "ne_address": "***************", "owner": "QJG", "console_url": "http://***************:7654", "console_trace": [{"slot": "EC11", "console": "208.12_P15"}], "slot_trace": [{"slot": "1"}, {"slot": "3"}, {"slot": "5"}]}]}], "monitor_parameter": {"ne": "Y", "slot": "Y", "port": "Y", "transceiver": "Y", "och": "Y", "entity": "Y", "alarm": "Y", "console_trace": "Y", "slot_trace": "Y", "push_to_remote": "Y"}, "netconf_parameter": {"port": "5122", "username": "root", "password": "Nokia#123"}, "cli_parameter": {"port": "5122", "username": "root", "password": "Nokia#123"}, "ssh_parameter": {"port": "5122", "username": "root", "password": "Nokia#123"}}